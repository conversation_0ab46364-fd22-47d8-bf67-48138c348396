// source: annofeedv1/data-.proto
package biz

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/davecgh/go-spew/spew"
	"path"
	"time"

	"annofeed/internal/data/serial"
	"annofeed/internal/signer"

	"gitlab.rp.konvery.work/platform/apis/anno/v1"
	"gitlab.rp.konvery.work/platform/apis/annofeed/v1"
	"gitlab.rp.konvery.work/platform/apis/client"
	"gitlab.rp.konvery.work/platform/apis/types"
	"gitlab.rp.konvery.work/platform/pkg/data/field"
	"gitlab.rp.konvery.work/platform/pkg/data/repo"
	"gitlab.rp.konvery.work/platform/pkg/errors"
	"gitlab.rp.konvery.work/platform/pkg/kfs"
	"gitlab.rp.konvery.work/platform/pkg/kid"
	"gitlab.rp.konvery.work/platform/pkg/log"

	"github.com/samber/lo"
)

const MaxElementsPerQuery = 1000
const MaxSummaryItems = 10

type DataState = client.DataState

const (
	DataStateRaw        = client.DataStateRaw
	DataStateFetching   = client.DataStateFetching
	DataStateProcessing = client.DataStateProcessing
	DataStateReady      = client.DataStateReady
	DataStateAbandoned  = client.DataStateAbandoned
	DataStateDisabled   = client.DataStateDisabled
	DataStateFailed     = client.DataStateFailed
)

func FromPbDataState(state annofeed.Data_State_Enum) DataState {
	return DataState(state.String())
}

// TODO: change data type to Enum
const (
	DataTypeImage      = "image"      // one image in an element
	DataTypePointcloud = "pointcloud" // one point-cloud file in an element
	DataTypeFusion2D   = "fusion2d"   // multiple image files in an element
	DataTypeFusion3D   = "fusion3d"   // multiple image files and a point-cloud file in an element
	DataTypeFusion4D   = "fusion4d"   // a multi-frame fusion point-cloud file and related pictures in a frame series
)

type DataSource = serial.Type[*anno.Source]
type DataSummary = serial.Type[*anno.DataValidationSummary]
type Data struct {
	ID int64 `json:"id" gorm:"default:null"`
	// Uid        string     `json:"uid" gorm:"default:null"`
	BaseOn     int64       `json:"base_on" gorm:"default:null"`
	Name       string      `json:"name" gorm:"default:null"`
	Desc       string      `json:"desc" gorm:"default:null"`
	Type       string      `json:"type" gorm:"default:null"`
	Source     DataSource  `json:"source" gorm:"default:null"`
	Size       int32       `json:"size" gorm:"default:null"`
	State      DataState   `json:"state" gorm:"default:null"`
	Error      string      `json:"error" gorm:"default:null"`
	CreatorUid string      `json:"creator_uid" gorm:"default:null"`
	OrgUid     string      `json:"org_uid" gorm:"default:null"`
	OrderUid   string      `json:"order_uid" gorm:"default:null"`
	Hierarchy  string      `json:"hierarchy" gorm:"default:null"`
	Summary    DataSummary `json:"summary" gorm:"default:null"`
	CreatedAt  time.Time   `json:"created_at" gorm:"default:null"`
	UpdatedAt  time.Time   `json:"updated_at" gorm:"default:null"`
	DeletedAt  DeletedAt   `json:"deleted_at" gorm:"default:null"`
	// Parser     string     `json:"parser" gorm:"default:null"`
}

func (Data) TableName() string { return "datas" }

func (o *Data) GetID() int64 { return o.ID }
func (o *Data) EnsureID() {
	if o.ID == 0 {
		o.ID = kid.NewID()
	}
}
func (o *Data) GetUid() string { return kid.StringID(o.ID) }
func (o *Data) UidFld() string { panic("code error") }

type DataSfld string

func (o DataSfld) String() string    { return string(o) }
func (o DataSfld) WithTable() string { return Data{}.TableName() + "." + string(o) }

var (
	data_             = field.RegObject(&Data{})
	DataUpdatableFlds = field.NewModel(Data{}.TableName(),
		data_, "BaseOn", "Name", "Size", "State", "Error", "Desc", "Type", "Source", "OrgUid", "Summary")

	DataSfldID         = DataSfld(field.Sname(&data_.ID))
	DataSfldBaseOn     = DataSfld(field.Sname(&data_.BaseOn))
	DataSfldName       = DataSfld(field.Sname(&data_.Name))
	DataSfldDesc       = DataSfld(field.Sname(&data_.Desc))
	DataSfldType       = DataSfld(field.Sname(&data_.Type))
	DataSfldSource     = DataSfld(field.Sname(&data_.Source))
	DataSfldSize       = DataSfld(field.Sname(&data_.Size))
	DataSfldState      = DataSfld(field.Sname(&data_.State))
	DataSfldError      = DataSfld(field.Sname(&data_.Error))
	DataSfldOrgUid     = DataSfld(field.Sname(&data_.OrgUid))
	DataSfldOrderUid   = DataSfld(field.Sname(&data_.OrderUid))
	DataSfldCreatorUid = DataSfld(field.Sname(&data_.CreatorUid))
	DataSfldSummary    = DataSfld(field.Sname(&data_.Summary))
)

type DataListFilter struct {
	IDs         []int64
	OrderUid    string
	OrgUid      string
	CreatorUid  string
	NamePattern string
	States      []DataState

	BizgranteeUid string // to check kam's grants
}

func (o *DataListFilter) Apply(tx Tx) Tx {
	if tx == nil {
		return nil
	}

	tbl := Data{}.TableName() + "."
	tx = repo.ApplyFieldFilter(tx, tbl+"id", o.IDs)
	tx = repo.ApplyFieldFilter(tx, DataSfldCreatorUid.WithTable(), o.CreatorUid)
	tx = repo.ApplyFieldFilter(tx, DataSfldOrgUid.WithTable(), o.OrgUid)
	tx = repo.ApplyFieldFilter(tx, DataSfldOrderUid.WithTable(), o.OrderUid)
	tx = repo.ApplyPatternFilter(tx, DataSfldName.WithTable(), o.NamePattern)
	tx = repo.ApplyFieldFilter(tx, DataSfldState.WithTable(), o.States)

	if o.BizgranteeUid != "" {
		f := &repo.JoinFilter{
			LeftModel:    &Data{},
			LeftJoinFld:  DataSfldOrgUid.String(),
			RightModel:   &Bizgrant{},
			RightJoinFld: BizgrantSfldOrgUid.String(),
			Filter:       &BizgrantFilter{GranteeUid: o.BizgranteeUid},
		}
		tx = f.Apply(tx)
	}
	return tx
}

type BackgroundTask interface {
	PrepareData(context.Context, *Data) error
}

type DatasRepo interface {
	repo.GenericRepo[Data]

	ListByFilter(context.Context, *DataListFilter, Pager) ([]*Data, error)
}

type DatasBiz struct {
	repo   DatasRepo
	rdrepo RawdataRepo
	rdbiz  *RawdataBiz
	bg     BackgroundTask
	// parserMgr   ParserMgr
	log *log.Helper
}

func NewDatasBiz(repo DatasRepo, rdrepo RawdataRepo, rdbiz *RawdataBiz, bg BackgroundTask, logger log.Logger) *DatasBiz {
	return &DatasBiz{repo: repo, rdrepo: rdrepo, rdbiz: rdbiz, bg: bg, log: log.NewHelper(logger)}
}

func (o *DatasBiz) Create(ctx context.Context, p *Data) (ret *Data, err error) {
	o.log.Info(ctx, "CreateData", "data", p)

	// create new data based on an existing data
	if p.BaseOn > 0 {
		if err = o.AlignBaseOn(ctx, p); err != nil {
			return
		}
	}

	// make order id the same as data id
	if p.ID == 0 {
		p.ID = kid.NewID()
	}
	if p.OrderUid == "sameid" {
		p.OrderUid = kid.StringID(p.ID)
	}

	err = o.repo.DoTx(ctx, func(ctx context.Context, tx Tx) error {
		p.State = DataStateRaw
		ret, err = o.repo.Create(ctx, p)
		if err != nil {
			return err
		}

		err = client.CreateAccessPolicies(ctx, PermClsData, p.GetUid(),
			[]string{client.UserScope(p.CreatorUid)}, []string{client.GroupScope(p.OrgUid)})
		if err != nil {
			return err
		}

		if !p.Source.E.AutoParse {
			return nil
		}

		fmt.Println("---> create feed datas: ", ret)
		return o.bg.PrepareData(ctx, ret)
	})
	if err != nil {
		return nil, err
	}
	return
}

// AlignBaseOn checks the base data, and makes it aligned with its base data.
func (o *DatasBiz) AlignBaseOn(ctx context.Context, p *Data) error {
	d, err := o.repo.GetByID(ctx, p.BaseOn)
	if err != nil {
		return err
	}
	if p.OrgUid != "" && p.OrgUid != d.OrgUid {
		return errors.NewErrFailedPrecondition(errors.WithMessage("base data is not in your org"))
	}
	if d.State != DataStateReady {
		return errors.NewErrFailedPrecondition(errors.WithMessage("base data is not ready"))
	}
	if d.BaseOn > 0 {
		p.BaseOn = d.BaseOn
	}
	p.Type = d.Type
	p.Size = d.Size
	p.OrgUid = d.OrgUid
	return nil
}

func (o *DatasBiz) Update(ctx context.Context, p *Data, fldMask *FieldMask) (*Data, error) {
	o.log.Info(ctx, "UpdateData", "data", p)
	return o.repo.Update(ctx, p, fldMask)
}

func (o *DatasBiz) SetState(ctx context.Context, p *Data, state DataState) (*Data, error) {
	p.State = state
	return o.Update(ctx, p, field.NewMask("state"))
}

func (o *DatasBiz) GetByID(ctx context.Context, id int64) (*Data, error) {
	return o.repo.GetByID(ctx, id)
}

func (o *DatasBiz) GetByUid(ctx context.Context, uid string) (*Data, error) {
	return o.GetByID(ctx, kid.ParseID(uid))
}

func (o *DatasBiz) DeleteByID(ctx context.Context, id int64) error {
	o.log.Info(ctx, "DeleteByIDData", "id", id)
	return o.repo.DoTx(ctx, func(ctx context.Context, tx Tx) error {
		if err := o.repo.DeleteByID(ctx, id); err != nil {
			return err
		}
		return client.DeleteAccessPolicies(ctx, PermClsData, kid.StringID(id))
	})
}

func (o *DatasBiz) DeleteByUid(ctx context.Context, uid string) error {
	o.log.Info(ctx, "DeleteByUidData", "uid", uid)
	return o.DeleteByID(ctx, kid.ParseID(uid))
}

func (o *DatasBiz) List(ctx context.Context, filter *DataListFilter, pager Pager) ([]*Data, error) {
	o.log.Info(ctx, "ListData", "filter", filter, "pager", pager)
	return o.repo.ListByFilter(ctx, filter, pager)
}

func (o *DatasBiz) Count(ctx context.Context, filter *DataListFilter) (int64, error) {
	return o.repo.Count(ctx, filter)
}

func (o *DatasBiz) GetDataElements(ctx context.Context, req *annofeed.GetDataElementsRequest) (*annofeed.GetDataElementsReply, error) {
	fmt.Println("---> GetDataElements", req)
	totalStart := time.Now()
	defer func() {
		fmt.Printf(">>>>>> TOTAL PROCESSING TIME: %v\n", time.Since(totalStart))
	}()
	// ======================= DEBUGGING CODE START =======================
	spew.Dump(req, ctx)
	// 模拟一个耗时3秒的慢查询，这肯定会超过客户端2秒的超时
	//fmt.Println("Simulating a slow query for 3 seconds...")
	//time.Sleep(2 * time.Second)
	//fmt.Println("Finished simulation.")
	// ======================= DEBUGGING CODE END =========================

	datasetStart := time.Now()
	// fetch dataset
	ds, err := o.GetByUid(ctx, req.Uid)
	if err != nil {
		return nil, err
	}
	if ds.State != DataStateReady {
		return nil, errors.NewErrFailedPrecondition(errors.WithMessage("data is not ready"))
	}
	fmt.Printf(">>>>>> Dataset fetch time: %v\n", time.Since(datasetStart))

	dataID := ds.ID
	if ds.BaseOn > 0 {
		// load rawdatas from base data
		dataID = ds.BaseOn
	}

	rawdataStart := time.Now()
	// fetch rawdatas
	rawdatas, err := o.rdrepo.ListByFilter(ctx, &RawdataListFilter{
		DataID:       dataID,
		FromElemIdx:  req.StartIdx,
		ElemCnt:      req.Count,
		NotTypeAnnos: ds.BaseOn > 0,
	})
	if err != nil {
		return nil, err
	}
	fmt.Printf(">>>>>> Rawdata fetch time: %v, count: %d\n", time.Since(rawdataStart), len(rawdatas))
	if len(rawdatas) == 0 {
		return &annofeed.GetDataElementsReply{Elements: nil}, nil
	}
	fmt.Println("---> get data elements data: ", rawdatas)
	//time.Sleep(2 * time.Second)

	// ======================= BUILD ELEMENTS START =======================
	elemBuildStart := time.Now()
	defer func() {
		fmt.Printf(">>>>>> Element build total time: %v\n", time.Since(elemBuildStart))
	}()
	// build elements
	elems := make([]*anno.Element, req.Count)
	lastIdx, cnt := rawdatas[0].ElemIdxInData, 0
	series := path.Dir(rawdatas[0].Folder)
	// 性能监控变量
	var (
		paramParseTime    time.Duration
		annoUnmarshalTime time.Duration
		convertTime       time.Duration
		paramApplyTime    time.Duration
		urlGenTime        time.Duration
		loopCount         int
		jsonOpCount       int
	)
	var param *ElemParamGroup
	for _, rd := range rawdatas {
		loopCount++

		// do not cross series
		dirCheckStart := time.Now()
		if path.Dir(rd.Folder) != series {
			break
		}
		dirCheckTime := time.Since(dirCheckStart)
		if dirCheckTime > 1*time.Millisecond {
			fmt.Printf(">>>>>> Dir check took long: %v for rawdata %d\n", dirCheckTime, loopCount)
		}
		indexUpdateStart := time.Now()
		if lastIdx != rd.ElemIdxInData {
			lastIdx = rd.ElemIdxInData
			cnt++
		}
		indexUpdateTime := time.Since(indexUpdateStart)
		if indexUpdateTime > 500*time.Microsecond {
			fmt.Printf(">>>>>> Index update took long: %v for rawdata %d\n", indexUpdateTime, loopCount)
		}

		// get or create element
		elem := elems[cnt]
		if elem == nil {
			elem = &anno.Element{
				Name:  rd.Folder,
				Type:  anno.Element_Type_Enum(anno.Element_Type_Enum_value[ds.Type]),
				Index: rd.ElemIdxInData,
			}
			elems[cnt] = elem
		}
		switch rd.Type {
		case RawdataTypeParams:
			paramStart := time.Now()
			param, err = ParseElemParam(rd.Info)
			paramParseTime += time.Since(paramStart)
			if err != nil {
				return nil, fmt.Errorf("failed to parse param %v: %w", rd.Name, err)
			}
			continue
		case RawdataTypeAnnos:
			if len(rd.Info) == 0 {
				continue
			}
			annoStart := time.Now()
			elem.Anno = &ElemAnno{}
			err = json.Unmarshal(rd.Info, elem.Anno)
			annoUnmarshalTime += time.Since(annoStart)
			jsonOpCount++
			if err != nil {
				return nil, fmt.Errorf("failed to parse annotations %v: %w", rd.Name, err)
			}
			continue
		case RawdataTypeImage:
		case RawdataTypePointcloud:
		default:
			o.log.Warn(ctx, "ignore rawdata: type %v", rd.Type)
			continue
		}

		convertStart := time.Now()
		// append rawdata
		data := ConvertRawdata(rd)
		convertTime += time.Since(convertStart)

		paramApplyStart := time.Now()
		if param != nil {
			switch rd.Type {
			case RawdataTypeImage:
				specifiedTitle := param.GetImageTitle(rd.CamName)
				data.Title = lo.Ternary(specifiedTitle != "", specifiedTitle, data.Title)
				transformStart := time.Now()
				data.Transform, err = param.GetImageTransforms(rd.CamName)
				paramApplyTime += time.Since(transformStart)
				if err != nil {
					return nil, fmt.Errorf("failed to get image transforms %v: %w", rd.CamName, err)
				}
			case RawdataTypePointcloud:
				transformStart := time.Now()
				data.Transform, err = param.GetLidarTransforms(kfs.FileBareName(rd.Name))
				paramApplyTime += time.Since(transformStart)
				if err != nil {
					return nil, fmt.Errorf("failed to get lidar transforms: %w", err)
				}
			}
		}
		paramApplyTime += time.Since(paramApplyStart)

		urlStart := time.Now()
		if data.Embedding != nil && data.Embedding.Url != "" {
			data.Embedding.Url = o.rdbiz.EmbeddingRoute(rd)
		}
		data.Url = o.rdbiz.DataRoute(rd)
		urlGenTime += time.Since(urlStart)
		elem.Datas = append(elem.Datas, data)
	}
	// 打印循环内部性能指标
	fmt.Printf(">>>>>> Element build loop stats: \n"+
		"  Loops: %d\n"+
		"  Param parse time: %v\n"+
		"  Anno unmarshal time: %v (count: %d)\n"+
		"  Convert time: %v\n"+
		"  Param apply time: %v\n"+
		"  URL gen time: %v\n",
		loopCount, paramParseTime, annoUnmarshalTime, jsonOpCount, convertTime, paramApplyTime, urlGenTime)
	elems = elems[:cnt+1]

	if ds.BaseOn > 0 {
		baseOnStart := time.Now()
		// load annotations from data
		rds, err := o.rdrepo.ListByFilter(ctx, &RawdataListFilter{
			DataID:      ds.ID,
			FromElemIdx: req.StartIdx,
			ElemCnt:     req.Count,
		})
		if err != nil {
			return nil, err
		}
		fmt.Printf(">>>>>> BaseOn rawdata fetch time: %v, count: %d\n", time.Since(baseOnStart), len(rds))

		//annoProcessStart := time.Now()
		var annoUnmarshalCount int
		var annoUnmarshalTime time.Duration
		for _, elem := range elems {
			for _, rd := range rds {
				if elem.Index == rd.ElemIdxInData && rd.Type == RawdataTypeAnnos {
					unmarshalStart := time.Now()
					elem.Anno = &ElemAnno{}
					err = json.Unmarshal(rd.Info, elem.Anno)
					annoUnmarshalTime += time.Since(unmarshalStart)
					annoUnmarshalCount++
					if err != nil {
						return nil, fmt.Errorf("failed to parse annotations %v: %w", rd.Name, err)
					}
					break
				}
			}
		}
		fmt.Printf(">>>>>> BaseOn annotation processing: \n"+
			"  Elements: %d\n"+
			"  Annotations processed: %d\n"+
			"  Unmarshal time: %v\n",
			len(elems), annoUnmarshalCount, annoUnmarshalTime)

		fmt.Printf(">>>>>> BaseOn total processing time: %v\n", time.Since(baseOnStart))
	}
	fmt.Printf(">>>>>> FINAL Element count: %d\n", len(elems))
	//fmt.Printf(">>>>>> Data processing loop took: %v for %d rawdata rows", time.Since(startTime), len(rawdatas))
	return &annofeed.GetDataElementsReply{Elements: elems}, nil
}

func (o *DatasBiz) FindDataElements(ctx context.Context, req *annofeed.FindDataElementsRequest) (*annofeed.GetDataElementsReply, error) {
	// fetch dataset
	ds, err := o.GetByUid(ctx, req.Uid)
	if err != nil {
		return nil, err
	}
	if ds.State != DataStateReady {
		return nil, errors.NewErrFailedPrecondition(errors.WithMessage("data is not ready"))
	}
	dataID := ds.ID
	if ds.BaseOn > 0 {
		// load rawdatas from base data
		dataID = ds.BaseOn
	}

	// fetch rawdatas
	filter := &RawdataListFilter{
		DataID:        dataID,
		FromElemIdx:   req.StartIdx,
		FolderPattern: req.NamePattern,
	}
	var rawdatas []*Rawdata
	flds := []string{RawdataSfldDataID, RawdataSfldElemIdxInData, RawdataSfldFolder}
	_, err = o.rdrepo.GroupBy(ctx, flds, nil, filter, repo.Pager{Pagesz: int(req.Count)}, &rawdatas)
	if err != nil {
		return nil, err
	}
	elems := lo.Map(rawdatas, func(rd *Rawdata, _ int) *anno.Element {
		return &anno.Element{
			Name:  rd.Folder,
			Index: rd.ElemIdxInData,
		}
	})
	return &annofeed.GetDataElementsReply{Elements: elems}, nil
}

func (o *DatasBiz) ParseData(ctx context.Context, req *annofeed.ParseDataRequest) error {
	o.log.Info(ctx, "ParseData", "req", req)
	dataID := kid.ParseID(req.Uid)
	data, err := o.GetByID(ctx, dataID)
	if err != nil {
		return err
	}
	switch data.State {
	case DataStateFetching, DataStateProcessing:
		return errors.NewErrFailedPrecondition(errors.WithMessage("data is processing"))
	}

	return o.repo.DoTx(ctx, func(ctx context.Context, tx Tx) error {
		switch req.Option {
		case annofeed.ParseDataRequest_force:
			// 无论上次解析结果如何，都清除之前的解析结果并重新解析数据
			if _, err := o.SetState(ctx, data, DataStateRaw); err != nil {
				return err
			}
			if err := o.rdbiz.DeleteByFilter(ctx, &RawdataDeleteFilter{DataID: data.ID}); err != nil {
				return err
			}
		case annofeed.ParseDataRequest_reparse_failed:
			// 解析未曾解析的或者失败的数据（清除之前的解析结果）
			if data.State != DataStateRaw && data.State != DataStateFailed {
				return errors.NewErrFailedPrecondition(errors.WithMessage("data is already processed successfully"))
			}
			if err := o.rdbiz.DeleteByFilter(ctx, &RawdataDeleteFilter{DataID: data.ID}); err != nil {
				return err
			}
		default: // unspecified
			// 只解析未曾解析的数据
			if data.State != DataStateRaw {
				return errors.NewErrFailedPrecondition(errors.WithMessage("data is already processed"))
			}
		}

		return o.bg.PrepareData(ctx, data)
	})
}

func (o *DatasBiz) GetDataMeta(ctx context.Context, req *annofeed.GetDataMetaRequest) (*annofeed.GetDataMetaReply, error) {
	data, err := o.GetByUid(ctx, req.GetUid())
	if err != nil {
		return nil, err
	}
	resp := &annofeed.GetDataMetaReply{Metadata: data.Source.E.GetMetadata()}
	if !req.WithMetafiles {
		return resp, nil
	}

	rawdatas, err := o.rdbiz.List(ctx, &RawdataListFilter{
		DataID: kid.ParseID(req.GetUid()),
		Type:   RawdataTypeMeta,
		Count:  1,
	})
	if err != nil {
		return nil, err
	}
	if len(rawdatas) == 0 {
		return resp, nil
	}

	resp.Metafiles = &types.Filelist{}
	if e := rawdatas[0].ExtraData.E; e != nil {
		for _, metafile := range e.Metafiles {
			res, err := signer.SignGetURL(ctx, metafile.URI, 0)
			if err != nil {
				return nil, err
			}
			resp.Metafiles.Files = append(resp.Metafiles.Files, &types.Filelist_File{
				Url:  res.URL,
				Name: metafile.Name,
			})
		}
	}

	return resp, nil
}
