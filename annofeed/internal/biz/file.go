package biz

import (
	"context"
	"path"
	"time"

	"annofeed/api/client"

	"gitlab.rp.konvery.work/platform/pkg/data/field"
	"gitlab.rp.konvery.work/platform/pkg/data/repo"
	"gitlab.rp.konvery.work/platform/pkg/kid"
	"gitlab.rp.konvery.work/platform/pkg/ktime"
	"gitlab.rp.konvery.work/platform/pkg/log"
)

const FileURLPathPrefix = "/pfile/"

type File struct {
	ID         int64     `json:"id" gorm:"default:null"`
	ShareID    string    `json:"share_id" gorm:"default:null"` // should be unguessable
	Name       string    `json:"name" gorm:"default:null"`
	URI        string    `json:"uri" gorm:"default:null"`
	MIME       string    `json:"mime" gorm:"default:null"`
	Size       int64     `json:"size" gorm:"default:null"`
	Sha256     string    `json:"sha256" gorm:"default:null;column:sha256"`
	State      int       `json:"state" gorm:"default:null"`
	UploadID   string    `json:"upload_id" gorm:"default:null"`
	CreatorUid string    `json:"creator_uid" gorm:"default:null"`
	OrgUid     string    `json:"org_uid" gorm:"default:null"`
	CreatedAt  time.Time `json:"created_at" gorm:"default:null"`
	DeletedAt  DeletedAt `json:"deleted_at" gorm:"default:null"`
}

func (o *File) GetID() int64   { return o.ID }
func (o *File) GetUid() string { return kid.StringID(o.ID) }
func (o *File) UidFld() string { panic("code error") }
func (o *File) EnsureID() {
	if o.ID == 0 {
		o.ID = kid.NewID()
	}
}
func (o *File) IsCompressed() bool {
	return o.MIME == "application/zip" || o.MIME == "application/x-zip-compressed"
}

const FileTableName = "files"

var (
	file_             = field.RegObject(&File{})
	FileUpdatableFlds = field.NewModel(FileTableName, file_, "ShareID", "State", "UploadID")

	FileSfldName       = field.Sname(&file_.Name)
	FileSfldURI        = field.Sname(&file_.URI)
	FileSfldSize       = field.Sname(&file_.Size)
	FileSfldSha265     = field.Sname(&file_.Sha256)
	FileSfldState      = field.Sname(&file_.State)
	FileSfldUploadID   = field.Sname(&file_.UploadID)
	FileSfldShareID    = field.Sname(&file_.ShareID)
	FileSfldOrgUid     = field.Sname(&file_.OrgUid)
	FileSfldCreatorUid = field.Sname(&file_.CreatorUid)
)

const (
	FileStateUploading = iota
	FileStateUploaded
)

type FileListFilter struct {
	OrgUid      string
	CreatorUid  string
	NamePattern string
	Sha256      string
	URIs        []string
}

func (o *FileListFilter) Apply(tx Tx) Tx {
	if tx == nil {
		return nil
	}

	tbl := FileTableName + "."
	tx = repo.ApplyFieldFilter(tx, tbl+FileSfldCreatorUid, o.CreatorUid)
	tx = repo.ApplyFieldFilter(tx, tbl+FileSfldOrgUid, o.OrgUid)
	tx = repo.ApplyFieldFilter(tx, tbl+FileSfldSha265, o.Sha256)
	tx = repo.ApplyFieldFilter(tx, tbl+FileSfldURI, o.URIs)
	tx = repo.ApplyPatternFilter(tx, tbl+FileSfldName, o.NamePattern)
	return tx
}

type FilesRepo interface {
	repo.GenericRepo[File]

	GetByShareID(context.Context, string) (*File, error)
}

type FilesBiz struct {
	repo FilesRepo
	log  *log.Helper
}

func NewFilesBiz(repo FilesRepo, logger log.Logger) *FilesBiz {
	return &FilesBiz{repo: repo, log: log.NewHelper(logger)}
}

func (o *FilesBiz) Create(ctx context.Context, p *File) (file *File, err error) {
	o.log.Info(ctx, "CreateFile", "file", p)
	err = o.repo.DoTx(ctx, func(ctx context.Context, tx Tx) error {
		file, err = o.repo.Create(ctx, p)
		if err != nil {
			return err
		}

		return client.CreateAccessPolicies(ctx, PermClsFile, p.GetUid(),
			[]string{client.UserScope(p.CreatorUid)}, []string{client.GroupScope(p.OrgUid)})
	})
	if err != nil {
		return nil, err
	}
	return
}

func (o *FilesBiz) Update(ctx context.Context, p *File, fldMask *FieldMask) (*File, error) {
	o.log.Info(ctx, "UpdateFile", "file", p)
	return o.repo.Update(ctx, p, fldMask)
}

func (o *FilesBiz) GetByID(ctx context.Context, id int64) (*File, error) {
	return o.repo.GetByID(ctx, id)
}

func (o *FilesBiz) GetByUid(ctx context.Context, uid string) (*File, error) {
	return o.GetByID(ctx, kid.ParseID(uid))
}

func (o *FilesBiz) GetByShareID(ctx context.Context, id string) (*File, error) {
	return o.repo.GetByShareID(ctx, id)
}

func (o *FilesBiz) DeleteByID(ctx context.Context, id int64) error {
	o.log.Info(ctx, "DeleteByIDFile", "id", id)
	return o.repo.DoTx(ctx, func(ctx context.Context, tx Tx) error {
		// TODO: delete file from the storage
		if err := o.repo.DeleteByID(ctx, id); err != nil {
			return err
		}
		return client.DeleteAccessPolicies(ctx, PermClsFile, kid.StringID(id))
	})
}

func (o *FilesBiz) DeleteByUid(ctx context.Context, uid string) error {
	o.log.Info(ctx, "DeleteByUidFile", "uid", uid)
	return o.DeleteByID(ctx, kid.ParseID(uid))
}

func (o *FilesBiz) List(ctx context.Context, filter *FileListFilter, pager Pager) (
	files []*File, nextPageToken PageToken, err error) {
	o.log.Info(ctx, "ListFile", "filter", filter)
	return o.repo.List(ctx, filter, pager)
}

func (o *FilesBiz) Count(ctx context.Context, filter *FileListFilter) (int64, error) {
	return o.repo.Count(ctx, filter)
}

const uploadFileKeyRoot = "upload"

func MakeUploadFileKey(uid, name string) string {
	return path.Join(uploadFileKeyRoot, ktime.Today(ktime.DateStyleShortPath), uid, name)
}
