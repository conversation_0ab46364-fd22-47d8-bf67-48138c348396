package jobstat

import (
	"context"
	"fmt"

	"annostat/api/client"
	"annostat/internal/biz"
	"annostat/workflow/common"

	"gitlab.rp.konvery.work/platform/pkg/kid"
	"gitlab.rp.konvery.work/platform/pkg/log"

	"github.com/go-kratos/kratos/v2/errors"
	"gitlab.rp.konvery.work/platform/apis/anno/v1"
)

type Activities struct {
	jobstatRepo biz.JobstatsRepo
	log         *log.Helper
}

func NewActivities(jobstatRepo biz.JobstatsRepo, logger log.Logger) *Activities {
	return &Activities{
		jobstatRepo: jobstatRepo,
		log:         log.NewHelper(logger),
	}
}

func (o *Activities) OnJobsubmit(ctx context.Context, ev *common.Event) error {
	fmt.Println("---> annostat fetch job annos")
	// fetch job annos
	ctx = client.NewCtxUseSvcAccount(ctx)
	job, err := client.GetJob(ctx, kid.StringID(ev.JobID), false)
	fmt.Println(err)
	if err != nil {
		return fmt.Errorf("failed to GetJob: %w", err)
	}

	// load job stat
	jstat, err := o.jobstatRepo.GetByFilter(ctx, &biz.JobstatListFilter{JobID: ev.JobID})
	if err != nil {
		if !errors.IsNotFound(err) {
			return fmt.Errorf("failed to query jobstat: %w", err)
		}
		jstat = &biz.Jobstat{
			LotID:   ev.LotID,
			JobID:   ev.JobID,
			Cuboids: make(map[string]biz.JobstatCuboid, 10),
		}
	}
	// load lot stat
	lfilter := biz.NewAndFilters(&biz.JobstatListFilter{LotID: ev.LotID}, biz.NewEqualFilter(biz.JobstatSfldJobID, 0))
	lstat, err := o.jobstatRepo.GetByFilter(ctx, lfilter)
	if err != nil {
		if !errors.IsNotFound(err) {
			return fmt.Errorf("failed to query jobstat: %w", err)
		}
		lstat = &biz.Jobstat{
			LotID:   ev.LotID,
			Cuboids: make(map[string]biz.JobstatCuboid, 10),
		}
	}

	// update the stat by subtracting job's old stat and add its new stat
	for k, v := range jstat.Cuboids {
		lstat.Cuboids[k] = lstat.Cuboids[k].Minus(v)
	}
	fmt.Println(job.AnnotationsUrl)
	if job.AnnotationsUrl != "" {
		annoData, err := biz.DownloadData[anno.Job_AnnotationData](ctx, job.AnnotationsUrl)
		if err != nil {
			return err
		}
		job.Annotations = annoData.GetElementAnnos()
	}
	jstat.Cuboids = sumCuboidObjs(job.Annotations)
	for k, v := range jstat.Cuboids {
		lstat.Cuboids[k] = lstat.Cuboids[k].Add(v)
	}

	// update job and lot stat
	_, err = biz.Upsert[biz.Jobstat](ctx, o.jobstatRepo, jstat, biz.JobstatSfldCuboids)
	if err != nil {
		return fmt.Errorf("failed to update jobstat: %w", err)
	}
	_, err = biz.Upsert[biz.Jobstat](ctx, o.jobstatRepo, lstat, biz.JobstatSfldCuboids)
	if err != nil {
		return fmt.Errorf("failed to update jobstat: %w", err)
	}

	return nil
}

func sumCuboidObjs(elemAnnos []*anno.ElementAnno) map[string]biz.JobstatCuboid {
	labels := make(map[string]biz.JobstatCuboid, 10)
	if elemAnnos == nil || len(elemAnnos) == 0 {
		return labels
	}
	for _, e := range elemAnnos {
		if e == nil || e.RawdataAnnos == nil || len(e.RawdataAnnos) == 0 {
			continue
		}
		for _, rd := range e.RawdataAnnos {
			if rd == nil || rd.Objects == nil || len(rd.Objects) == 0 {
				continue
			}
			for _, obj := range rd.Objects {
				if obj == nil || obj.Label == nil || obj.Label.Widget == nil || obj.Label.Widget.Data == nil || len(obj.Label.Widget.Data) < 6 {
					continue
				}
				if obj.GetLabel().GetWidget().GetName() == anno.WidgetName_cuboid {
					c := labels[obj.Label.Name]
					c.InsCnt++
					for i, v := range obj.Label.Widget.Data[3:6] {
						c.Scales[i] += v
					}
					labels[obj.Label.Name] = c
				}
			}
		}
	}
	return labels
}
