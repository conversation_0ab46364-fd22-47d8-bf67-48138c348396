package biz

import (
	"bytes"
	"compress/gzip"
	"context"
	"fmt"
	"io"
	"os"
	"strings"

	"annostat/internal/data/serial"
	"annostat/workflow/common"

	"gitlab.rp.konvery.work/platform/pkg/data/field"
	"gitlab.rp.konvery.work/platform/pkg/data/repo"
	"gitlab.rp.konvery.work/platform/pkg/download"

	"github.com/go-kratos/kratos/v2/encoding"
	"github.com/google/wire"
	"gorm.io/datatypes"
	"gorm.io/gorm"
)

// ProviderSet is biz providers.
var ProviderSet = wire.NewSet() // NewJobsBiz, NewLotsBiz, NewOrdersBiz

type JSON = datatypes.JSON
type DeletedAt = gorm.DeletedAt
type Tx = *gorm.DB
type FieldMask = field.Mask
type TxAction func(ctx context.Context, v any) error

func JSONCodec() encoding.Codec { return encoding.GetCodec("json") }

func CleanTx(tx Tx) Tx { return tx.Session(&gorm.Session{NewDB: true}) }

type StrMap = serial.Map[string, string]

type (
	Pager           = repo.Pager
	TimeRange       = repo.TimeRange
	ListFilter      = repo.ListFilter
	JoinFilter      = repo.JoinFilter
	GroupLastFilter = repo.GroupLastFilter
	EqualFilter     = repo.EqualFilter
	UnequalFilter   = repo.UnequalFilter
	RelationFilter  = repo.RelationFilter
)

const (
	FilterRelationGreaterThan = repo.FilterRelationGreaterThan
	FilterRelationNotIn       = repo.FilterRelationNotIn
)

var (
	ApplyFieldFilter     = repo.ApplyFieldFilter
	ApplyPatternFilter   = repo.ApplyPatternFilter
	ApplyTimeRangeFilter = repo.ApplyTimeRangeFilter
	NewAndFilters        = repo.NewAndFilters
	NewEqualFilter       = repo.NewEqualFilter
)

type ValidValue[T any] struct {
	Value T
	Valid bool
}

func NewValidValue[T any](v T) ValidValue[T] {
	return ValidValue[T]{
		Value: v,
		Valid: true,
	}
}

func (o *ValidValue[T]) Set(v T) {
	o.Value = v
	o.Valid = true
}

type BackgroundTask interface {
	StartWorkflow(ctx context.Context, wfid string, wffunc any, args ...any) error
	SignalEvent(ctx context.Context, wfid string, ev *common.Event) error
	SignalEventWithStart(ctx context.Context, wfid string, ev *common.Event, wffunc any, wfArgs ...any) error
}

type GenericRepo[Model any] interface {
	DoTx(ctx context.Context, fn func(ctx context.Context, tx Tx) error) (err error)

	Create(context.Context, *Model) (*Model, error)
	BatchCreate(ctx context.Context, logs []*Model) ([]*Model, error)
	Update(context.Context, *Model, *FieldMask) (*Model, error)
	BatchUpdate(context.Context, *Model, *FieldMask, ListFilter) error
	GetByID(ctx context.Context, id int64) (*Model, error)
	GetByFilter(context.Context, ListFilter) (*Model, error)
	DeleteByID(context.Context, int64) error
	DeleteByIDs(context.Context, []int64) error
	List(context.Context, ListFilter, Pager) ([]*Model, error)
	Count(context.Context, ListFilter) (int64, error)
	CountUniqueValue(context.Context, ListFilter, string) (int64, error)
	GroupBy(ctx context.Context, groupFlds, aggrFlds []string, p ListFilter, pager Pager, res any) error
	GroupCountStrFld(context.Context, string, ListFilter, Pager) (map[string]int64, error)
}

func Upsert[T any](ctx context.Context, repo repo.GenericRepo[T], data *T, updateFlds ...string) (v *T, err error) {
	t, ok := any(data).(IDGetter)
	if !ok {
		return nil, fmt.Errorf("failed to upsert %T: not IDGetter", data)
	}
	if t.GetID() == 0 {
		v, err = repo.Create(ctx, data)
	} else {
		v, err = repo.Update(ctx, data, field.NewMask(updateFlds...))
	}
	// if err != nil {
	// 	return nil, fmt.Errorf("failed to upsert %T: %w", data, err)
	// }
	return
}

func DownloadData[T any](ctx context.Context, uri string) (data *T, err error) {
	fpath, err := download.Download(ctx, &download.File{URI: uri})
	if err != nil {
		return nil, fmt.Errorf("failed to download file: %w", err)
	}
	defer os.RemoveAll(fpath)

	fdata, err := os.ReadFile(fpath)
	if err != nil {
		return nil, fmt.Errorf("failed to read file: %w", err)
	}
	// 判断是否gzip数据 解压缩
	if strings.HasSuffix(uri, ".gz") || isGzipData(fdata) {
		fmt.Println("gzip file detected")
		fdata, err = DecompressGzipData(fdata)
		if err != nil {
			return nil, fmt.Errorf("failed to decompress file: %w", err)
		}
		fmt.Println("gzip decompression successful")
	}
	if err := JSONCodec().Unmarshal(fdata, &data); err != nil {
		return nil, fmt.Errorf("failed to unmarshal data to elements: %w", err)
	}
	return data, nil
}

func isGzipData(data []byte) bool {
	return len(data) > 2 && data[0] == 0x1F && data[1] == 0x8B
}

// DecompressGzipData decompresses the input GZIP data and returns the decompressed byte slice.
func DecompressGzipData(data []byte) ([]byte, error) {
	zr, err := gzip.NewReader(bytes.NewReader(data))
	if err != nil {
		return nil, fmt.Errorf("failed to create gzip reader: %w", err)
	}
	defer zr.Close()
	// Log the internal file name (if available).
	fmt.Println("Decompressed file name:", zr.Name)
	decompressedData, err := io.ReadAll(zr)
	if err != nil {
		return nil, fmt.Errorf("failed to decompress gzip data: %w", err)
	}
	return decompressedData, nil
}
