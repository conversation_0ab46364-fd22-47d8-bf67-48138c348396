package reap

import (
	"context"
	_ "embed"
	"github.com/davecgh/go-spew/spew"

	"annout/internal/biz"

	"gitlab.rp.konvery.work/platform/pkg/kparser"
	"gitlab.rp.konvery.work/platform/pkg/kutil"
	"gitlab.rp.konvery.work/platform/pkg/wf/ktwf"
)

//go:embed wf.yaml
var wfTplRaw []byte
var _ = newWorkflow() // check workflow spec

func newWorkflow() *ktwf.WorkflowSpec {
	spec, err := ktwf.ParseWorkflowSpec(wfTplRaw)
	kutil.Assert(err, "failed to parse workflow template")
	return spec
}

func WorkflowID(lotUid string) string { return "annout-" + lotUid }

func StartWorkflow(ctx context.Context, data *biz.Reaper) error {
	spec := newWorkflow()
	spec.Workflow.Args["data"] = kparser.ToJSONStr(data)
	spew.Dump("---> start workflow: ", data)
	_, err := ktwf.StartWorkflow(ctx, WorkflowID(data.LotUid), spec, true)
	return err
}
