#!/usr/bin/env python3
"""
简化版本的 Reaper - 只实现数据收集和本地保存
功能：收集注释数据 -> 打包保存到本地
"""

import os
import json
import zipfile
import logging
import psycopg2
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional
from dataclasses import dataclass

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# UID 转换相关常量和函数
BASE_CHARS = 'abcdefghijklmnopqrstuvwxyz345678'


def kw_parse_uid(uid: str) -> int:
    """
    Parses a UID string into a bigint (integer).
    This function mimics the behavior of the SQL function kw_parse_uid.
    """
    id_value = 0
    for char_c in uid:
        try:
            char_index = BASE_CHARS.index(char_c)
            id_value = (id_value << 5) + char_index
        except ValueError:
            raise ValueError(f"Error: Character '{char_c}' from UID '{uid}' not found in BASE_CHARS.")
    return id_value


def kw_to_uid(id_value: int) -> str:
    """
    Converts a bigint (integer) into a UID string.
    This function mimics the behavior of the SQL function kw_to_uid.
    """
    uid = ""
    while id_value > 0:
        char_index = id_value & 31
        char_to_prepend = BASE_CHARS[char_index]
        uid = char_to_prepend + uid
        id_value = id_value >> 5
    return uid


@dataclass
class DatabaseConfig:
    """数据库配置"""
    host: str = "localhost"
    port: int = 5432
    user: str = "root"
    password: str = "root"
    database: str = "anno"


@dataclass
class SimpleConfig:
    """简化的配置类"""
    lot_ids: List[str]  # 支持多个批次ID
    phases: List[int] = None
    finished_only: bool = False  # 是否只获取已完成的作业
    output_dir: str = "./output"
    db_config: DatabaseConfig = None

    def __post_init__(self):
        if self.phases is None:
            self.phases = []
        if self.db_config is None:
            self.db_config = DatabaseConfig()
        # 兼容单个 lot_id 的配置
        if isinstance(self.lot_ids, str):
            self.lot_ids = [self.lot_ids]


class DatabaseClient:
    """PostgreSQL 数据库客户端"""

    def __init__(self, db_config: DatabaseConfig):
        self.db_config = db_config
        logger.info(f"初始化 PostgreSQL 数据库客户端: {db_config.host}:{db_config.port}/{db_config.database}")
        self._test_connection()

    def _test_connection(self):
        """测试数据库连接"""
        try:
            conn = self._get_connection()
            conn.close()
            logger.info("数据库连接测试成功")
        except Exception as e:
            logger.error(f"数据库连接失败: {e}")
            raise

    def _get_connection(self):
        """获取数据库连接"""
        return psycopg2.connect(
            host=self.db_config.host,
            port=self.db_config.port,
            user=self.db_config.user,
            password=self.db_config.password,
            database=self.db_config.database
        )

    def get_lot_info(self, lot_id: str) -> Dict[str, Any]:
        """获取批次信息"""
        logger.info(f"获取批次信息: {lot_id}")
        lot_id_int = kw_parse_uid(lot_id)

        conn = self._get_connection()
        cursor = conn.cursor()

        try:
            # 查询批次基本信息
            cursor.execute('''
                SELECT id, name, data_uid, phase_count, ins_cnt, state, org_uid, order_id
                FROM lots
                WHERE id = %s AND deleted_at IS NULL
            ''', (lot_id_int,))

            lot_result = cursor.fetchone()
            if not lot_result:
                raise ValueError(f"批次 {lot_id} 不存在")

            lot_db_id, name, data_uid, phase_count, ins_cnt, state, org_uid, order_id = lot_result

            # 查询作业统计信息
            cursor.execute('''
                SELECT
                    COUNT(*) as total_jobs,
                    COUNT(CASE WHEN finished = true THEN 1 END) as finished_jobs,
                    MIN(phase) as min_phase,
                    MAX(phase) as max_phase
                FROM jobs
                WHERE lot_id = %s
            ''', (lot_id_int,))

            job_result = cursor.fetchone()
            total_jobs, finished_jobs, min_phase, max_phase = job_result or (0, 0, None, None)

            phases = list(range(min_phase, max_phase + 1)) if min_phase and max_phase else list(range(1, phase_count + 1))

            return {
                "uid": lot_id,
                "name": name,
                "data_uid": data_uid,
                "order_uid": kw_to_uid(order_id) if order_id else "",
                "org_uid": org_uid,
                "phases": phases,
                "phase_count": phase_count,
                "ins_cnt": ins_cnt,
                "state": state,
                "total_jobs": total_jobs,
                "finished_jobs": finished_jobs
            }
        finally:
            conn.close()

    def list_jobs(self, lot_id: str, page: int = 0, page_size: int = 10,
                  phases: List[int] = None, finished_only: bool = False) -> Dict[str, Any]:
        """获取作业列表"""
        logger.info(f"查询作业: lot_id={lot_id}, page={page}, phases={phases}, finished_only={finished_only}")

        lot_id_int = kw_parse_uid(lot_id)

        conn = self._get_connection()
        cursor = conn.cursor()

        try:
            # 构建查询条件
            where_conditions = ["lot_id = %s"]
            params = [lot_id_int]

            if phases:
                placeholders = ",".join(["%s"] * len(phases))
                where_conditions.append(f"phase IN ({placeholders})")
                params.extend(phases)

            if finished_only:
                where_conditions.append("finished = true")

            where_clause = " AND ".join(where_conditions)

            # 查询作业
            offset = page * page_size
            query = f'''
                SELECT id, lot_id, idx_in_lot, elems, phase, submits, finished, duration
                FROM jobs
                WHERE {where_clause}
                ORDER BY idx_in_lot
                LIMIT %s OFFSET %s
            '''

            cursor.execute(query, params + [page_size, offset])
            rows = cursor.fetchall()

            # 转换为作业格式
            jobs = []
            for row in rows:
                job_id, lot_id_db, idx_in_lot, elems, phase, submits, finished, duration = row

                # TODO: 从实际注释表获取注释数据
                # 这里需要根据实际的注释表结构来查询注释数据
                annotations = self._get_job_annotations(job_id)

                jobs.append({
                    "uid": kw_to_uid(job_id),
                    "lot_uid": lot_id,
                    "idx_in_lot": idx_in_lot,
                    "phase": phase,
                    "finished": bool(finished),
                    "elems": elems,
                    "annotations": annotations,
                    "job_attrs": [
                        {"name": "phase", "values": [str(phase)]},
                        {"name": "duration", "values": [str(duration)]},
                        {"name": "finished", "values": [str(finished)]}
                    ]
                })

            return {"jobs": jobs}
        finally:
            conn.close()

    def _get_job_annotations(self, job_id: int) -> List[Dict[str, Any]]:
        """获取作业的实际注释数据"""
        # TODO: 根据实际的注释表结构实现
        # 这里需要查询实际的注释表，例如：
        # SELECT * FROM annotations WHERE job_id = %s

        # 暂时返回空列表，需要根据实际注释表结构实现
        logger.warning(f"作业 {job_id} 的注释数据获取功能待实现")
        return []


class SimpleReaper:
    """简化的 Reaper 实现"""

    def __init__(self, db_client: DatabaseClient):
        self.db_client = db_client
    
    def run(self, config: SimpleConfig) -> List[str]:
        """运行简化的工作流，支持多个批次"""
        output_files = []

        for lot_id in config.lot_ids:
            try:
                logger.info(f"开始处理批次: {lot_id}")

                # 步骤1: 收集注释数据
                annotations = self._collect_annotations_for_lot(lot_id, config)

                # 步骤2: 保存到本地
                output_file = self._save_to_local(lot_id, annotations, config)
                output_files.append(output_file)

                logger.info(f"批次 {lot_id} 处理完成，输出文件: {output_file}")

            except Exception as e:
                logger.error(f"批次 {lot_id} 处理失败: {e}")
                # 继续处理其他批次
                continue

        logger.info(f"所有批次处理完成，共生成 {len(output_files)} 个文件")
        return output_files
    
    def _collect_annotations_for_lot(self, lot_id: str, config: SimpleConfig) -> List[Dict[str, Any]]:
        """收集指定批次的注释数据"""
        logger.info(f"开始收集批次 {lot_id} 的注释数据...")

        all_annotations = []
        page = 0
        page_size = 10

        while True:
            # 获取当前页的作业
            response = self.db_client.list_jobs(
                lot_id,
                page=page,
                page_size=page_size,
                phases=config.phases,
                finished_only=config.finished_only
            )

            jobs = response.get("jobs", [])
            if not jobs:
                break

            # 处理作业数据
            for job in jobs:
                # 处理注释数据
                self._process_job_annotations(job)

                # 收集注释
                for annotation in job.get("annotations", []):
                    all_annotations.append(annotation)

            page += 1
            logger.info(f"批次 {lot_id} 已处理第 {page} 页，当前收集到 {len(all_annotations)} 个注释")

        logger.info(f"批次 {lot_id} 注释收集完成，总计: {len(all_annotations)} 个")
        return all_annotations
    
    def _process_job_annotations(self, job: Dict[str, Any]):
        """处理作业注释数据（添加作业属性、确保唯一性等）"""
        # 添加作业属性前缀
        job_attrs = []
        for attr in job.get("job_attrs", []):
            job_attrs.append({
                "name": f"job_attr:{attr['name']}",
                "values": attr["values"]
            })
        
        # 处理每个注释
        for annotation in job.get("annotations", []):
            # 添加作业属性到注释
            annotation["attrs"] = annotation.get("attrs", []) + job_attrs
            
            # 确保 track_id 在批次内唯一
            for rawdata_anno in annotation.get("rawdata_annos", []):
                for obj in rawdata_anno.get("objects", []):
                    if obj.get("track_id"):
                        obj["track_id"] = f"job{job['idx_in_lot']:05d}.{obj['track_id']}"
    
    def _save_to_local(self, lot_id: str, annotations: List[Dict[str, Any]], config: SimpleConfig) -> str:
        """保存数据到本地，按文件夹整理"""
        logger.info(f"开始保存批次 {lot_id} 的数据到本地...")

        # 创建输出目录
        output_dir = Path(config.output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)

        # 获取批次信息
        lot_info = self.db_client.get_lot_info(lot_id)

        # 生成文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        phases_str = "_".join(map(str, config.phases)) if config.phases else "all"
        finished_str = "_finished" if config.finished_only else "_all"

        # 创建批次专用目录
        lot_dir = output_dir / f"lot_{lot_id}_{timestamp}"
        lot_dir.mkdir(parents=True, exist_ok=True)

        # 按文件夹整理注释数据
        annotations_by_folder = self._organize_annotations_by_folder(annotations)

        # 保存元数据
        meta_data = {
            "lot_id": lot_id,
            "lot_info": lot_info,
            "phases": config.phases,
            "finished_only": config.finished_only,
            "total_annotations": len(annotations),
            "folders": list(annotations_by_folder.keys()),
            "export_time": datetime.now().isoformat(),
            "format_version": "1.0"
        }

        with open(lot_dir / "metadata.json", 'w', encoding='utf-8') as f:
            json.dump(meta_data, f, indent=2, ensure_ascii=False)

        # 按文件夹保存注释
        for folder_name, folder_annotations in annotations_by_folder.items():
            folder_dir = lot_dir / "annotations" / folder_name
            folder_dir.mkdir(parents=True, exist_ok=True)

            # 保存该文件夹的注释
            for i, annotation in enumerate(folder_annotations):
                filename = f"{annotation['name']}.json"
                with open(folder_dir / filename, 'w', encoding='utf-8') as f:
                    json.dump(annotation, f, indent=2, ensure_ascii=False)

        # 创建汇总 ZIP 文件
        zip_filename = f"lot_{lot_id}_phases_{phases_str}{finished_str}_{timestamp}.zip"
        zip_path = output_dir / zip_filename

        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            # 添加所有文件到 ZIP
            for root, dirs, files in os.walk(lot_dir):
                for file in files:
                    file_path = Path(root) / file
                    arc_path = file_path.relative_to(lot_dir)
                    zipf.write(file_path, arc_path)

        logger.info(f"数据已保存到:")
        logger.info(f"  - 文件夹: {lot_dir}")
        logger.info(f"  - ZIP文件: {zip_path}")
        return str(zip_path)

    def _organize_annotations_by_folder(self, annotations: List[Dict[str, Any]]) -> Dict[str, List[Dict[str, Any]]]:
        """按文件夹整理注释数据"""
        folders = {}

        for annotation in annotations:
            # 根据注释的 rawdata_annos 中的文件信息来分组
            folder_name = "default"

            # 尝试从 rawdata_annos 中提取文件夹信息
            for rawdata_anno in annotation.get("rawdata_annos", []):
                data_name = rawdata_anno.get("name", "")
                if "/" in data_name:
                    # 如果包含路径分隔符，提取文件夹名
                    folder_name = data_name.split("/")[0]
                    break
                elif "_" in data_name:
                    # 如果包含下划线，可能是文件夹前缀
                    parts = data_name.split("_")
                    if len(parts) > 1:
                        folder_name = parts[0]
                        break

            # 如果没有找到合适的文件夹名，使用元素名的前缀
            if folder_name == "default":
                element_name = annotation.get("name", "")
                if "_" in element_name:
                    folder_name = element_name.split("_")[0]
                elif "-" in element_name:
                    folder_name = element_name.split("-")[0]

            if folder_name not in folders:
                folders[folder_name] = []

            folders[folder_name].append(annotation)

        logger.info(f"注释按文件夹整理完成: {list(folders.keys())}")
        return folders


if __name__ == "__main__":
    print("请使用 run_export.py 运行数据导出")
