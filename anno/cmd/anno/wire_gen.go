// Code generated by Wire. DO NOT EDIT.

//go:generate go run github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package main

import (
	"anno/internal/biz"
	"anno/internal/conf"
	"anno/internal/data"
	"anno/internal/server"
	"anno/internal/service"
	"anno/workflow"
	"anno/workflow/jobwf"
	"anno/workflow/lotwf"
	"github.com/go-kratos/kratos/v2/log"
)

// Injectors from wire.go:

// wireApp init kratos application.
func wireApp(confServer *conf.Server, confData *conf.Data, logger log.Logger) (*App, func(), error) {
	dataData, cleanup, err := data.NewData(confData, logger)
	if err != nil {
		return nil, nil, err
	}
	configRepo := data.NewConfigRepo(dataData, logger)
	configBiz := biz.NewConfigBiz(configRepo, logger)
	configsService := service.NewConfigsService(logger, configBiz)
	jobsRepo := data.NewJobsRepo(dataData, logger)
	lotsRepo := data.NewLotsRepo(dataData, logger)
	backgroundTask := workflow.NewWorkflowStarter(logger)
	jobsBiz := biz.NewJobsBiz(jobsRepo, lotsRepo, backgroundTask, logger)
	lotsBiz := biz.NewLotsBiz(lotsRepo, jobsRepo, backgroundTask, logger)
	jobelemRepo := data.NewJobelemRepo(dataData, logger)
	jobskipAnnotationRepo :=data.NewJobSkipannotationRepo(dataData,logger)
	jobSkipAnnotationBiz :=biz.NewJobSkipAnnotationRepoBiz(jobskipAnnotationRepo,logger)
	jobsService := service.NewJobsService(jobsBiz, lotsBiz, jobelemRepo, jobSkipAnnotationBiz,logger)
	ordersRepo := data.NewOrdersRepo(dataData, logger)
	ordersBiz := biz.NewOrdersBiz(ordersRepo, logger)
	lotsService := service.NewLotsService(logger, lotsBiz, lotsRepo, jobsBiz, ordersBiz, backgroundTask)
	lottplsRepo := data.NewLottplsRepo(dataData, logger)
	lottplsBiz := biz.NewLottplsBiz(lottplsRepo, logger)
	lottplsService := service.NewLottplsService(lottplsBiz)
	labelclzRepo := data.NewLabelclzRepo(dataData, logger)
	labelclzBiz := biz.NewLabelclzBiz(labelclzRepo, logger)
	labelclzService := service.NewLabelclzService(labelclzBiz)
	labelwidgetsRepo := data.NewLabelwidgetsRepo(dataData, logger)
	labelwidgetsBiz := biz.NewLabelwidgetsBiz(labelwidgetsRepo, logger)
	labelwidgetsService := service.NewLabelwidgetsService(labelwidgetsBiz)
	ordersService := service.NewOrdersService(logger, ordersBiz, lotsBiz)
	projectsRepo := data.NewProjectsRepo(dataData, logger)
	projectsBiz := biz.NewProjectsBiz(projectsRepo, logger)
	projectsService := service.NewProjectsService(projectsBiz)
	skillsRepo := data.NewSkillsRepo(dataData, logger)
	skillsBiz := biz.NewSkillsBiz(skillsRepo, logger)
	skillsService := service.NewSkillsService(skillsBiz)
	bizgrantsRepo := data.NewBizgrantsRepo(dataData, logger)
	bizgrantsBiz := biz.NewBizgrantsBiz(bizgrantsRepo, logger)
	bizgrantsService := service.NewBizgrantsService(bizgrantsBiz, bizgrantsRepo)
	specgrantsRepo := data.NewSpecgrantsRepo(dataData, logger)
	specgrantsBiz := biz.NewSpecgrantsBiz(specgrantsRepo, logger)
	specgrantsService := service.NewSpecgrantsService(specgrantsBiz, specgrantsRepo, lotsRepo)
	grpcServer := server.NewGRPCServer(confServer, configsService, jobsService, lotsService, lottplsService, labelclzService, labelwidgetsService, ordersService, projectsService, skillsService, bizgrantsService, specgrantsService, logger)
	httpServer := server.NewHTTPServer(confServer, configsService, jobsService, lotsService, lottplsService, labelclzService, labelwidgetsService, ordersService, projectsService, skillsService, bizgrantsService, specgrantsService, logger)
	activities := lotwf.NewActivities(lotsBiz, jobsBiz, ordersBiz, jobelemRepo, logger)
	jobwfActivities := jobwf.NewActivities(lotsBiz, jobsBiz, logger)
	app := newApp(logger, grpcServer, httpServer, activities, jobwfActivities)
	return app, func() {
		cleanup()
	}, nil
}
