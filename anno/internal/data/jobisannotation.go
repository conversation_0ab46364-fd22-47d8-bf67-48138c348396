// source: anno/v1/jobisannotation.proto
package data

import (
	biz "anno/internal/biz"
	context "context"
	log "github.com/go-kratos/kratos/v2/log"
)

type jobSkipannotationRepo struct {
	data *Data
	log  *log.Helper
}

func NewJobSkipannotationRepo(data *Data, logger log.Logger) biz.JobSkipAnnotationRepo {
	return &jobSkipannotationRepo{data: data, log: log.NewHelper(logger)}
}

func (o *jobSkipannotationRepo) Create(ctx context.Context, p *biz.JobSkipAnnotation) (*biz.JobSkipAnnotation, error) {
	return Create(ctx, o.data, p)
}

func (o *jobSkipannotationRepo) GetByID(ctx context.Context, id int64) (*biz.JobSkipAnnotation, error) {
	return GetByID[biz.JobSkipAnnotation](ctx, o.data, id)
}

func (o *jobSkipannotationRepo) DeleteByID(ctx context.Context, id int64) error {
	return DeleteByID[biz.JobSkipAnnotation](ctx, o.data, id)
}
